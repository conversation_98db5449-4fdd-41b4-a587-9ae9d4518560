"use client";
import { <PERSON>, <PERSON>Header, CardContent } from "@/common/components/ui/card";
import { Checkbox } from "@/common/components/ui/checkbox";
import { Input } from "@/common/components/ui/input";
import { Label } from "@/common/components/ui/label";
import { Vehicle } from "@/common/models";
import { useRouter } from "next/navigation";
import { useVehicleQuery } from "@/common/hooks/use-vehicle-query";
import { useSession } from "next-auth/react";
import { usdFormatter } from "@/common/lib/currency-utils";
import { Calendar, Clock, Gauge, Gift, Tag } from "lucide-react";
import { Badge } from "@/common/components/ui/badge";
import useCreateBookingQuote from "@/common/hooks/use-create-booking-quote";
import { VehicleBooking } from "@/common/models";
import { formatToISO8601 } from "@/common/lib/date-utils";
import { Button } from "@/common/components/ui/button";
import Loader from "@/common/components/loader";
import { PhoneInput } from "@/common/components/ui/phone-input";
import { useForm, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { cn } from "@/common/lib/shadcn-utils";
import { useState } from "react";
import useQuotedBooking from "@/common/hooks/use-quoted-booking";
import { useUserCurrency } from "@/common/hooks/use-user-preferences";
import {
  isKarlinkBrand,
  getCurrentBrandConfig,
  getWhatsAppLink,
} from "@/common/config/brands/utils";
import { MessageCircle } from "lucide-react";

interface Props {
  vehicle: Vehicle;
}

const formSchema = z.object({
  firstName: z
    .string()
    .min(1, "First name is required")
    .min(2, "First name must be at least 2 characters")
    .max(50, "First name must be less than 50 characters"),
  lastName: z
    .string()
    .min(1, "Last name is required")
    .min(2, "Last name must be at least 2 characters")
    .max(50, "Last name must be less than 50 characters"),
  email: z
    .string()
    .min(1, "Email is required")
    .email("Please enter a valid email address")
    .max(100, "Email must be less than 100 characters"),
  phone: z.string().min(1, "Phone number is required"),
  vehicleId: z.number().positive("Invalid vehicle ID"),
  addons: z
    .array(
      z.object({
        id: z.number(),
        name: z.string(),
        price: z.number(),
      }),
    )
    .default([]),

  terms: z
    .boolean()
    .refine(
      (value) => value === true,
      "You must accept the terms and conditions",
    ),
});

type FormData = z.infer<typeof formSchema>;

export default function BookVehicleForm({ vehicle }: Props) {
  const { saveQuotedBooking } = useQuotedBooking();
  const mutation = useCreateBookingQuote();
  const router = useRouter();
  const query = useVehicleQuery();
  const session = useSession();
  const { preferredCurrency } = useUserCurrency();
  const vehicleAddons = vehicle.inventory.filter((inv) => inv.price != null);
  const hasPromotion = vehicle.promotions && vehicle.promotions.length > 0;
  const promotion = vehicle.promotions[0];

  // Brand configuration for whitelabel detection
  const brandConfig = getCurrentBrandConfig();
  const showContactSales = !isKarlinkBrand();

  // Phone validation state
  const [phoneValidation, setPhoneValidation] = useState({
    isValid: false,
    error: null as string | null,
  });

  const {
    control,
    handleSubmit,
    formState: { errors, isValid, isSubmitting },
    setError,
  } = useForm<FormData>({
    resolver: zodResolver(formSchema),
    mode: "onChange",
    defaultValues: {
      firstName: session?.data?.user?.firstName || "",
      lastName: session?.data?.user?.lastName || "",
      email: session?.data?.user?.client?.email || "",
      phone: session?.data?.user?.client?.telephone || "",
      vehicleId: vehicle.id,
      addons: [],
      terms: false,
    },
  });

  // Calculate days until promotion expires
  const getDaysUntilExpiry = () => {
    if (!hasPromotion) return null;

    const expiryDate = new Date(promotion!.expiryDate as string);
    const today = new Date();
    const diffTime = expiryDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    return diffDays;
  };

  const daysUntilExpiry = getDaysUntilExpiry();

  const onSubmit = async (data: FormData) => {
    // Check phone validation before submitting
    if (!phoneValidation.isValid && data.phone) {
      setError("phone", {
        type: "manual",
        message: phoneValidation.error || "Please enter a valid phone number",
      });
      return;
    }

    try {
      const booking: Partial<
        VehicleBooking & {
          vehicleAddonsIds: number[];
          gatewayType: "STRIPE" | "PAYNOW";
          hirerCurrency?: string;
        }
      > = {
        firstname: data.firstName,
        surname: data.lastName,
        email: data.email,
        phone: data.phone,
        start: formatToISO8601(
          new Date(`${query.state.start!}T${query.state.startTime}`),
        ),
        end: formatToISO8601(
          new Date(`${query.state.end!}T${query.state.endTime}`),
        ),
        vehicleId: data.vehicleId,
        clientId: session?.data?.user?.client?.id,
        vehicleAddonsIds: data.addons.map((addon) => addon.id),
        gatewayType: "STRIPE",
        hirerCurrency: preferredCurrency,
      };

      const bookingQuote = await mutation.mutateAsync(booking);
      saveQuotedBooking(bookingQuote);
      router.push("/bookings/checkout");
    } catch (error) {
      console.error("Booking submission error:", error);
      // Handle error appropriately
    }
  };

  return (
    <Card className="overflow-hidden pt-0">
      <CardHeader className="p-0">
        {/* Promotion banner - top of form */}
        {hasPromotion && (
          <div
            className={`bg-linear-to-r ${getPromoBannerGradient(vehicle)} space-y-4 p-4 text-white`}
          >
            <div>
              <h3 className="text-xl font-bold">{promotion!.title}</h3>
              <p className="text-sm">{promotion!.description}</p>
            </div>

            {/* Promotion details */}
            <div className="mt-3 flex justify-between">
              <div>
                <div className="text-xs text-white/80">Benefits</div>
                <div className="flex items-center gap-1 font-medium">
                  <div>{getPromoIcon(vehicle)}</div>
                  {getPromoDetailValue(vehicle)}
                </div>
              </div>
              <div>
                <div className="text-xs text-white/80">Minimum Hire Period</div>
                <div className="flex items-center gap-1 font-medium">
                  <Clock className="size-4" />
                  {promotion?.daysHired}{" "}
                  {promotion?.daysHired === 1 ? "Day" : "Days"}
                </div>
              </div>
              {daysUntilExpiry && daysUntilExpiry > 0 && (
                <div>
                  <div className="text-xs text-white/80">Days Until Expiry</div>
                  <div className="flex items-center gap-1 font-medium">
                    <Clock className="size-4" />
                    {daysUntilExpiry} {daysUntilExpiry === 1 ? "Day" : "Days"}
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
        <div className="px-6 py-4">
          <h2 className="text-xl font-bold">Complete your booking</h2>
          <p className="text-foreground/80">
            Fill in your details below to proceed with the booking
          </p>

          {/* Price display */}
          {getPriceDisplay(vehicle)}
        </div>
      </CardHeader>
      <CardContent>
        <form className="grid gap-6" onSubmit={handleSubmit(onSubmit)}>
          <div className="grid gap-2">
            <Label htmlFor="firstName" className="text-foreground/80">
              First Name
            </Label>
            <Controller
              name="firstName"
              control={control}
              render={({ field }) => (
                <Input
                  {...field}
                  className={cn(
                    "h-auto p-3",
                    errors.firstName &&
                      "border-destructive focus-visible:ring-destructive",
                  )}
                  id="firstName"
                  placeholder="Enter your first name"
                />
              )}
            />
            {errors.firstName && (
              <p className="text-destructive text-sm">
                {errors.firstName.message}
              </p>
            )}
          </div>

          <div className="grid gap-2">
            <Label htmlFor="lastName" className="text-foreground/80">
              Last Name
            </Label>
            <Controller
              name="lastName"
              control={control}
              render={({ field }) => (
                <Input
                  {...field}
                  className={cn(
                    "h-auto p-3",
                    errors.lastName &&
                      "border-destructive focus-visible:ring-destructive",
                  )}
                  id="lastName"
                  placeholder="Enter your last name"
                />
              )}
            />
            {errors.lastName && (
              <p className="text-destructive text-sm">
                {errors.lastName.message}
              </p>
            )}
          </div>

          <div className="grid gap-2">
            <Label htmlFor="email" className="text-foreground/80">
              Email
            </Label>
            <Controller
              name="email"
              control={control}
              render={({ field }) => (
                <Input
                  {...field}
                  className={cn(
                    "h-auto p-3",
                    errors.email &&
                      "border-destructive focus-visible:ring-destructive",
                  )}
                  id="email"
                  placeholder="Enter your email"
                  type="email"
                  disabled={!!session?.data?.user?.client?.email}
                />
              )}
            />
            {errors.email && (
              <p className="text-destructive text-sm">{errors.email.message}</p>
            )}
          </div>

          <div className="grid gap-2">
            <Label htmlFor="phone" className="text-foreground/80">
              Phone Number
            </Label>
            <Controller
              name="phone"
              control={control}
              render={({ field: { onChange, value } }) => (
                <PhoneInput
                  value={value}
                  onChange={(phoneValue, isValid) => {
                    onChange(phoneValue);
                    setPhoneValidation({
                      isValid,
                      error: isValid ? null : "Invalid phone number",
                    });
                  }}
                  onValidationChange={(isValid, error) => {
                    setPhoneValidation({
                      isValid,
                      error: error || null,
                    });
                  }}
                  placeholder="Enter your phone number"
                  className="h-auto"
                  inputClassName="p-3"
                  error={!!errors.phone}
                />
              )}
            />
            {errors.phone && (
              <p className="text-destructive text-sm">{errors.phone.message}</p>
            )}
            {!errors.phone && phoneValidation.error ? (
              <p className="text-destructive text-sm">
                {phoneValidation.error}
              </p>
            ) : null}
          </div>

          {vehicleAddons.length > 0 && (
            <div className="grid gap-2">
              <Label className="text-foreground/80 text-base font-semibold">
                Vehicle Add-ons(Price per day)
              </Label>
              <Controller
                name="addons"
                control={control}
                render={({ field: { onChange, value } }) => (
                  <div className="grid gap-2 md:grid-cols-2 lg:grid-cols-3">
                    {vehicleAddons.map((inv) => (
                      <div key={inv.id} className="flex items-center gap-2">
                        <Checkbox
                          id={`addon-${inv.id}`}
                          checked={value.some((addon) => addon.id === inv.id)}
                          onCheckedChange={(checked) => {
                            if (checked) {
                              onChange([
                                ...value,
                                {
                                  id: inv.id,
                                  name: inv.name,
                                  price: inv.price,
                                },
                              ]);
                            } else {
                              onChange(
                                value.filter((addon) => addon.id !== inv.id),
                              );
                            }
                          }}
                        />
                        <Label
                          htmlFor={`addon-${inv.id}`}
                          className="text-foreground text-sm"
                        >
                          {inv.name} ({usdFormatter.format(inv.price)})
                        </Label>
                      </div>
                    ))}
                  </div>
                )}
              />
            </div>
          )}

          <div className="flex items-center gap-2.5">
            <Controller
              name="terms"
              control={control}
              render={({ field: { onChange, value } }) => (
                <Checkbox
                  id="terms"
                  checked={value}
                  onCheckedChange={onChange}
                />
              )}
            />
            <label htmlFor="terms" className="text-foreground text-sm">
              I accept the{" "}
              <a
                href="/terms-and-conditions"
                target="_blank"
                className="text-blue-500 hover:underline"
              >
                terms and conditions
              </a>{" "}
              in addition to the{" "}
              <span className="font-semibold text-blue-500">
                ${(vehicle.depositAmt ?? 0).toFixed(2)}
              </span>{" "}
              deposit for booking this vehicle
            </label>
          </div>

          <div className="space-y-3">
            <Button
              type="submit"
              size="lg"
              className="w-full p-4"
              disabled={
                !isValid ||
                isSubmitting ||
                mutation.isPending ||
                !phoneValidation.isValid
              }
            >
              {(isSubmitting || mutation.isPending) && <Loader />}
              {isSubmitting || mutation.isPending
                ? "Redirecting to checkout..."
                : "Book Now"}
            </Button>

            {showContactSales && (
              <Button
                type="button"
                variant="outline"
                size="lg"
                className="w-full p-4"
                onClick={() => {
                  const vehicleUrl = `${window.location.origin}/vehicles/${vehicle.id}`;
                  const message = `Hi! I'm interested in negotiating a rate for this vehicle: ${vehicle.name} ${vehicle.model}.

Vehicle Link: ${vehicleUrl}

I'd like to discuss pricing options. Thank you!`;

                  const whatsappLink = getWhatsAppLink(
                    brandConfig.contactInfo.phone,
                    message,
                  );
                  window.open(whatsappLink, "_blank");
                }}
              >
                <MessageCircle className="mr-2 h-4 w-4" />
                Contact Sales
              </Button>
            )}
          </div>
        </form>
      </CardContent>
    </Card>
  );
}

// Get promotion banner gradient based on type
const getPromoBannerGradient = (vehicle: Vehicle) => {
  const hasPromotion = vehicle.promotions && vehicle.promotions.length > 0;
  const promoType = vehicle.promotions[0]?.promotionType;
  if (!hasPromotion) return "";

  switch (promoType) {
    case "DISCOUNT":
      return "from-amber-500 to-orange-500";
    case "EXTRA_MILEAGE":
      return "from-emerald-500 to-green-500";
    case "EXTRA_DAYS":
      return "from-blue-500 to-indigo-500";
    case "OTHER_AWARD":
      return "from-purple-500 to-fuchsia-500";
    default:
      return "from-amber-500 to-orange-500";
  }
};

// Get promotion icon based on type
const getPromoIcon = (vehicle: Vehicle) => {
  const hasPromotion = vehicle.promotions && vehicle.promotions.length > 0;
  const promoType = vehicle.promotions[0]?.promotionType;

  if (!hasPromotion) return <Tag className="h-5 w-5" />;

  switch (promoType) {
    case "DISCOUNT":
      return <Tag className="size-4" />;
    case "EXTRA_MILEAGE":
      return <Gauge className="size-4" />;
    case "EXTRA_DAYS":
      return <Calendar className="size-4" />;
    case "OTHER_AWARD":
      return <Gift className="size-4" />;
    default:
      return <Tag className="size-4" />;
  }
};

// Get promotion detail value based on type
const getPromoDetailValue = (vehicle: Vehicle) => {
  const hasPromotion = vehicle.promotions && vehicle.promotions.length > 0;
  const promoType = vehicle.promotions[0]?.promotionType;
  const promotion = vehicle.promotions[0]!;

  if (!hasPromotion) return null;

  switch (promoType) {
    case "DISCOUNT":
      return `${promotion!.discount}% Off`;
    case "EXTRA_MILEAGE":
      return promotion!.extraMileage
        ? `+${promotion!.extraMileage}KM/Day`
        : null;
    case "EXTRA_DAYS":
      return promotion!.extraDays
        ? `+${promotion!.extraDays} Free ${promotion!.extraDays === 1 ? "Day" : "days"}`
        : null;
    case "OTHER_AWARD":
      return "Special offer";
    default:
      return null;
  }
};

// Get price display based on promotion type
const getPriceDisplay = (vehicle: Vehicle) => {
  const rate = vehicle.vehicleRates.find(
    (rate) => rate.weekDay === "MTF",
  )?.rate;
  const promotion = vehicle.promotions[0]!;
  const promoType = vehicle.promotions[0]?.promotionType;
  const discountedRate = promotion?.discount
    ? (rate ?? 0) - ((rate ?? 0) * promotion.discount) / 100
    : null;
  const rateDifference = (rate ?? 0) - (discountedRate ?? 0);

  switch (promoType) {
    case "DISCOUNT":
      return (
        <div className="mt-4 flex items-center gap-4">
          <div className="flex items-center">
            <span className="text-xl font-bold text-orange-600">
              {usdFormatter.format(discountedRate ?? 0)}/Day
            </span>
            <span className="text-foreground/80 ml-2 line-through">
              {usdFormatter.format(rate ?? 0)}
            </span>
          </div>
          <Badge
            variant="outline"
            className="border-orange-100 bg-orange-100 text-orange-800 hover:bg-orange-200"
          >
            Save {usdFormatter.format(rateDifference ?? 0)} per day
          </Badge>
        </div>
      );
    case "EXTRA_MILEAGE":
      return (
        <div className="mt-4 flex items-center gap-4">
          <div className="text-xl font-bold">
            {usdFormatter.format(rate ?? 0)}/Day
          </div>
          <Badge
            variant="outline"
            className="border-green-100 bg-green-100 text-green-800 hover:bg-green-200"
          >
            +{promotion!.extraMileage}KM/Day
          </Badge>
        </div>
      );
    case "EXTRA_DAYS":
      return (
        <div className="mt-4 flex items-center gap-4">
          <div className="text-xl font-bold">
            {usdFormatter.format(rate ?? 0)}
          </div>
          <Badge
            variant="outline"
            className="border-blue-100 bg-blue-100 text-blue-800 hover:bg-blue-200"
          >
            +{promotion!.extraDays} Free{" "}
            {promotion!.extraDays === 1 ? "day" : "days"}
          </Badge>
        </div>
      );
    default:
      return (
        <div className="mt-4 text-xl font-bold">
          {usdFormatter.format(rate ?? 0)}/Day
        </div>
      );
  }
};
