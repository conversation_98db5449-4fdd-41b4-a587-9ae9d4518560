import { MapPin } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { getCurrentBrandConfig } from "@/common/config/brands/utils";
import { DEFAULT_POPULAR_LOCATIONS } from "@/common/config/brands/default-locations";
import { cn } from "@/common/lib/shadcn-utils";

export default function PopularLocations() {
  const brandConfig = getCurrentBrandConfig();
  const locations = brandConfig.popularLocations || DEFAULT_POPULAR_LOCATIONS;
  
  const gridCols = locations.length % 3 === 0 ? "lg:grid-cols-3" : "lg:grid-cols-4";

  return (
    <section className="w-full bg-gray-50 py-20">
      <div className="container mx-auto px-4 md:px-6">
        <div className="mb-12 flex flex-col items-center justify-center space-y-4 text-center">
          <h2 className="text-3xl font-bold tracking-tighter text-primary sm:text-4xl md:text-5xl">
            Popular Locations
          </h2>
          <p className="max-w-[700px] text-gray-600 md:text-xl">
            With vehicles available across major cities, you can find the
            perfect rental car for your needs.
          </p>
        </div>
        <div className={cn("grid grid-cols-1 gap-6 md:grid-cols-2", gridCols)}>
          {locations.map((location) => (
            <Link
              key={location.slug}
              href={`vehicles/search?location=${location.slug}`}
              className="group relative overflow-hidden rounded-lg shadow-lg transition-transform hover:scale-105"
            >
              <Image
                src={location.image}
                alt={`${location.name} car rentals`}
                width={400}
                height={300}
                className="h-[200px] w-full object-cover"
              />
              <div className="absolute inset-0 flex flex-col justify-end bg-linear-to-t from-black/90 to-black/0 p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="mb-1 text-xl font-semibold text-white">
                      {location.name}
                    </h3>
                  </div>
                  <div className="flex h-8 w-8 items-center justify-center rounded-full bg-white/10 backdrop-blur-xs transition-colors group-hover:bg-white/20">
                    <MapPin className="h-4 w-4 text-white" />
                  </div>
                </div>
              </div>
            </Link>
          ))}
        </div>
      </div>
    </section>
  );
}
